<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.ibm.watson.aiops.connectors.template</groupId>
  <artifactId>connector-template</artifactId>
  <version>1.0-SNAPSHOT</version>
  <packaging>war</packaging>
  <properties>
    <maven.compiler.source>11</maven.compiler.source>
    <maven.compiler.target>11</maven.compiler.target>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>

  <repositories>
    <!-- <repository>
      <id>sdk repository</id>
      <url>file:///${project.basedir}/../repo</url>
    </repository> -->
    <repository>
      <id>ibm-artifactory</id>
      <name>ibm-artifactory</name>
      <url>https://na.artifactory.swg-devops.com/artifactory/hyc-connector-framework-team-maven-local</url>
      <releases><enabled>true</enabled></releases>
      <snapshots><enabled>true</enabled></snapshots>
  </repository>
  </repositories>
  <dependencies>
    <dependency>
      <groupId>jakarta.platform</groupId>
      <artifactId>jakarta.jakartaee-api</artifactId>
      <version>9.1.0</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.eclipse.microprofile</groupId>
      <artifactId>microprofile</artifactId>
      <version>5.0</version>
      <type>pom</type>
      <scope>provided</scope>
    </dependency>
    <!-- SDK -->
    <dependency>
      <groupId>com.ibm.cp4waiops.connectors</groupId>
      <artifactId>connectors-sdk</artifactId>
      <version>2.2.10</version> <!-- CONNECTOR_SDK_VERSION -->
    </dependency>
    <!-- CloudEvent object and associated builder -->
    <dependency>
      <groupId>io.cloudevents</groupId>
      <artifactId>cloudevents-core</artifactId>
      <version>2.2.0</version>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter</artifactId>
      <version>5.7.1</version>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
      <version>3.8.0</version>
      <scope>test</scope>
    </dependency>
    <dependency>
    <groupId>org.json</groupId>
    <artifactId>json</artifactId>
    <version>20231013</version>
    </dependency>
<!-- https://mvnrepository.com/artifact/org.glassfish/jakarta.json -->
    <dependency>
      <groupId>org.glassfish</groupId>
      <artifactId>jakarta.json</artifactId>
      <version>2.0.1</version>
      <scope>test</scope>
    </dependency>
    
    <!-- Adding this impl due to connector is using ConnectorManagerMicroprofileConfig and unit test complained it can't find ConfigProviderResolver' -->
    <dependency>
    	<groupId>org.apache.geronimo.config</groupId>
    	<artifactId>geronimo-config-impl</artifactId>
    	<version>1.0</version>
    	<scope>test</scope>
	</dependency>

    <!-- https://mvnrepository.com/artifact/jakarta.json/jakarta.json-api -->
    <dependency>
      <groupId>jakarta.json</groupId>
      <artifactId>jakarta.json-api</artifactId>
      <version>2.1.3</version>
      <scope>test</scope>
    </dependency>

    <dependency>
    <groupId>io.fusionauth</groupId>
    <artifactId>fusionauth-jwt</artifactId>
    <version>4.2.0</version>
    </dependency>    
  </dependencies>
  <build>
    <finalName>connector-template</finalName>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-war-plugin</artifactId>
        <version>3.3.1</version>
      </plugin>
      <plugin>
        <groupId>io.openliberty.tools</groupId>
        <artifactId>liberty-maven-plugin</artifactId>
        <version>3.3.4</version>
      </plugin>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.8.1</version>
      </plugin>
      <plugin>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>3.0.0-M5</version>
        <configuration>
          <systemPropertyVariables>
            <java.util.logging.config.file>src/test/resources/logging.properties</java.util.logging.config.file>
          </systemPropertyVariables>
        </configuration>
      </plugin>
      <plugin>
        <groupId>net.revelc.code.formatter</groupId>
        <artifactId>formatter-maven-plugin</artifactId>
        <version>2.18.0</version>
        <executions>
          <execution>
            <goals>
              <goal>format</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
        <version>0.8.7</version>
        <executions>
          <execution>
            <id>coverage-initialize</id>
            <goals>
              <goal>prepare-agent</goal>
            </goals>
          </execution>
          <execution>
            <id>coverage-report</id>
            <phase>test</phase>
            <goals>
              <goal>report</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
  <profiles>
    <profile>
      <id>liberty</id>
      <activation>
        <activeByDefault>true</activeByDefault>
      </activation>
      <build>
        <resources>
          <resource>
            <directory>${basedir}/src/main/resources</directory>
            <includes>
              <include>**/*.*</include>
            </includes>
          </resource>
          <resource>
            <directory>${basedir}/src/main/java</directory>
            <includes>
              <include>**/*.*</include>
            </includes>
          </resource>
          <resource>
            <filtering>true</filtering>
            <directory>${basedir}/src/main/liberty</directory>
            <includes>
              <include>**/*.*</include>
            </includes>
          </resource>
        </resources>
        <finalName>${project.artifactId}</finalName>
      </build>
    </profile>
  </profiles>
</project>
