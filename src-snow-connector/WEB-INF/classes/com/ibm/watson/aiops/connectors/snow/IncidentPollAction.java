/*
 *
 * IBM Confidential
 * OCO Source Materials
 *
 * 5737-M96
 * (C) Copyright IBM Corp. 2022, 2023 All Rights Reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U.S. Copyright Office.
 *
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 *
 */
package com.ibm.watson.aiops.connectors.snow;

import java.net.http.HttpResponse;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.logging.Level;
import java.util.logging.Logger;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import io.cloudevents.CloudEvent;
import io.micrometer.core.instrument.Counter;

public class IncidentPollAction implements Runnable {

    private Counter actionCounter;
    private Counter actionErrorCounter;
    private ConnectorConfiguration config;
    private String connMode;
    private String username;
    private HttpClientUtil httpClient;

    private ScheduledExecutorService executorService = null;

    private Connector connector;
    private CloudEvent ce;

    private int offset = 0;

    private boolean testMode = false;
    private int numPages = 1;
    // determines which range to get back from actionUtils, default to incident sync
    private String fasterPoll = ConnectorConfiguration.PROPERTY_INCIDENT_SYNC;

    static final Logger logger = Logger.getLogger(IncidentPollAction.class.getName());

    private AtomicBoolean stopDataCollection = new AtomicBoolean(false);

    public IncidentPollAction(ConnectorAction action) {

        actionCounter = action.getActionCounter();
        actionErrorCounter = action.getActionErrorCounter();

        config = action.getConfiguration();
        connMode = config.getConnMode();
        username = config.getUsername();

        httpClient = action.getHttpClient();
        connector = action.getConnector();

        actionCounter = action.getActionCounter();
        actionErrorCounter = action.getActionErrorCounter();

    }

    @Override
    public void run() {
        logger.log(Level.INFO, "Run Incident Poll Action");
        actionCounter.increment();

        if (connMode.equals(ConnectorConstants.HISTORICAL)) {
            logger.log(Level.INFO, "Start collecting historical data");
            fetchAndEmitIncident();
        } else {
            int interval;
            TimeUnit unit;
            int incidentSyncInterval = config.getIncidentSyncInterval();
            String incidentSyncIntervalUnit = config.getIncidentSyncIntervalUnit();
            int similarIncidentInterval = config.getSimilarIncidentInterval();
            String similarIncidentIntervalUnit = config.getSimilarIncidentIntervalUnit();

            // check if incident sync or similar incident polling is toggled on
            boolean isSimilarIncidentToggled = config.getSimilarIncidentToggle();
            boolean isIncidentSyncToggled = config.getIncidentSyncToggle();
            // Incident polling happens for both similar incidents as well as incident sync, if both are toggled, use
            // the faster poll
            if (isSimilarIncidentToggled && isIncidentSyncToggled) {
                // convert both to seconds and use the lower value
                int incidentSyncSeconds = ActionUtils.convertToSeconds(incidentSyncInterval, incidentSyncIntervalUnit);
                int similarIncidentSeconds = ActionUtils.convertToSeconds(similarIncidentInterval,
                        similarIncidentIntervalUnit);
                if (similarIncidentSeconds < incidentSyncSeconds) {
                    interval = similarIncidentInterval;
                    unit = ActionUtils.convertToTimeUnit(similarIncidentIntervalUnit);
                    fasterPoll = ConnectorConfiguration.PROPERTY_SIMILAR_INCIDENT;
                } else {
                    interval = incidentSyncInterval;
                    unit = ActionUtils.convertToTimeUnit(incidentSyncIntervalUnit);
                    fasterPoll = ConnectorConfiguration.PROPERTY_INCIDENT_SYNC;
                }
            } else if (isSimilarIncidentToggled) {
                interval = similarIncidentInterval;
                unit = ActionUtils.convertToTimeUnit(similarIncidentIntervalUnit);
                fasterPoll = ConnectorConfiguration.PROPERTY_SIMILAR_INCIDENT;
            } else {
                interval = incidentSyncInterval;
                unit = ActionUtils.convertToTimeUnit(incidentSyncIntervalUnit);
                fasterPoll = ConnectorConfiguration.PROPERTY_INCIDENT_SYNC;
            }

            // Create a single-threaded executor
            executorService = Executors.newSingleThreadScheduledExecutor();
            logger.log(Level.INFO, "fetchAndEmitIncident with interval: " + interval + " and unit: " + unit);
            executorService.scheduleAtFixedRate(this::fetchAndEmitIncident, 0, interval, unit);

        }
    }

    public void stop() {
        logger.log(Level.INFO, "stop(): incident stop called");
        // This variable needs to be set as the shutdown of the executorService
        // will not force stop the polling from stopping
        stopDataCollection.set(true);

        if (executorService != null) {
            logger.log(Level.INFO, "stop(): Stopping incident polling thread");
            executorService.shutdownNow();
            logger.log(Level.INFO, "Incident polling stopped");
        }
    }

    private void fetchAndEmitIncident() {
        logger.log(Level.INFO, "Calling fetchAndEmitIncident");
        String path;
        String range = ActionUtils.getRange(config, fasterPoll);
        String bodyString = "";
        JSONObject body;
        JSONArray result;
        CompletableFuture<HttpResponse<String>> cFResp;
        HttpResponse<String> res;

        boolean hasNextPage = true;
        int pageCounter = 0;

        int incidentCounter = 0;

        // Reset the offset so subsequent calls start at the start
        offset = 0;
        while (hasNextPage) {
            try {
                if (stopDataCollection.get()) {
                    logger.log(Level.INFO, "Data flow changed to stopped, terminating data gathering");
                    break;
                }
                path = ConnectorConstants.INCIDENT_QUERY_API_PATH + ConnectorConstants.QUERY_GENERAL_PROPERTIES
                        + ConnectorConstants.QUERY_OFFSET + offset + ConnectorConstants.QUERY_PAGING
                        + ConnectorConstants.QUERY_UPDATED_BETWEEN + range;

                // for incident sync, exclude updates made by the integration user to avoid an update loop
                if (config.getIncidentSyncToggle()) {
                    path += ConnectorConstants.QUERY_EXCLUDE_USER + ActionUtils.encodeString(username);
                }

                if (!config.getIncidentSyncToggle() && config.getSimilarIncidentToggle()
                        || ConnectorConstants.HISTORICAL.equals(config.getConnMode())) {
                    path += ConnectorConstants.QUERY_STATE_CLOSED;
                }

                logger.log(Level.INFO, "Query path: " + path);

                cFResp = httpClient.get(path);
                res = cFResp.get();
                bodyString = res.body();
                body = new JSONObject(bodyString.trim());
                result = body.getJSONArray("result");

                for (int i = 0; i < result.length(); i++) {
                    if (stopDataCollection.get()) {
                        logger.log(Level.INFO, "Data flow changed to stopped, terminating data gathering");
                        break;
                    }
                    ce = getIncidentCE(result.getJSONObject(i));
                    connector.emitCloudEvent(ConnectorConstants.TOPIC_OUTPUT_INCIDENT, connector.getPartition(), ce);
                    incidentCounter++;
                }

                if (result.length() == 0) {
                    hasNextPage = false;
                }

                offset += result.length();

            } catch (InterruptedException e) {
                logger.log(Level.INFO, "Interrupt caused by a valid stop, polling stopped");
                break;
            } catch (Exception e) {
                actionErrorCounter.increment();
                logger.log(Level.SEVERE, "Could not get response ", e);
                connector.triggerAlerts(config, e, bodyString);
            }
            pageCounter++;
            if (testMode && numPages >= pageCounter) {
                break;
            }
        }

        logger.log(Level.INFO, "Number of incidents found: " + incidentCounter);
        connector.clearAlerts(config, ConnectorConstants.ALL_ALERTS_MINUS_IBM_APP, connector.getTimeLastCleared());

    }

    private CloudEvent getIncidentCE(JSONObject incident) {
        JSONObject json = new JSONObject();
        String[] keys = { ConnectorConstants.SYS_ID, ConnectorConstants.NUMBER, "short_description", "description",
                "u_caused_by_change", "caused_by", "close_code", "u_detection_source", "close_notes", "u_environment",
                "cmdb_ci", "comments_and_work_notes", "impact", "problem_id", "priority", "severity", "state",
                "resolved_at", "closed_at", "opened_at", "parent", "made_sla", "watch_list", "upon_reject",
                "sys_updated_on", "child_incidents", "hold_reason", "approval_history", "resolved_by", "sys_updated_by",
                "opened_by", "user_input", "sys_created_on", "sys_domain", "sys_created_by", "knowledge", "order",
                "calendar_stc", "delivery_plan", "contract", "active", "business_service", "sys_domain_path", "rfc",
                "time_worked", "expected_start", "business_duration", "group_list", "work_end", "caller_id",
                "reopened_time", "approval_set", "subcategory", "correlation_display", "delivery_task", "work_start",
                "assignment_group", "additional_assignee_list", "business_stc", "calendar_duration", "notify",
                "service_offering", "sys_class_name", "closed_by", "follow_up", "parent_incident", "contact_type",
                "reopened_by", "incident_state", "urgency", "company", "reassignment_count", "activity_due",
                "assigned_to", "comments", "approval", "sla_due", "due_date", "sys_mod_count", "reopen_count",
                "sys_tags", "escalation", "upon_approval", "correlation_id", "location", "category",
                "work_notes_list" };

        for (String key : keys) {
            if (incident.has(key)) {
                Object obj = incident.get(key);
                json.put(key, obj.toString());
            }
        }

        String workNotes = "";
        if (incident.has(ConnectorConstants.WORK_NOTES)) {
            workNotes = incident.getString(ConnectorConstants.WORK_NOTES);
        }
        String[] workNotesArr = workNotes.split("\\n\\n");
        json.put(ConnectorConstants.WORK_NOTES, workNotesArr);

        json.put("source_name", "service now");
        ActionUtils.applyMappings(incident, json, config.getMapping());
        json.put(ConnectorConstants.RAW_DATA, incident);

        String source = null;
        // The reason why the number may be null is from our auto tests
        try {
            Object numberObj = json.get(ConnectorConstants.NUMBER);
            if (numberObj != null) {
                source = ActionUtils.getSourceIdentifier(config.getURL(), ConnectorConstants.SOURCE_IDENTIFIER_INCIDENT,
                        numberObj.toString());
            }
        } catch (JSONException je) {
            // Do nothing, the number should normally be a required field
        }

        if (connMode.equals(ConnectorConstants.HISTORICAL)) {
            json.put(ConnectorConstants.CE_CONNECTION_MODE, ConnectorConstants.HISTORICAL);
        } else {
            json.put(ConnectorConstants.CE_CONNECTION_MODE, ConnectorConstants.LIVE);
        }

        json.put(ConnectorConstants.INCIDENTS_SYNC_FROM_SNOW_TO_AIOPS, String.valueOf(config.getIncidentSyncToggle()));

        json.put(ConnectorConstants.CONNECTION_ID, config.getConnectionID());
        json.put(ConnectorConstants.INSTANCE, ActionUtils.getInstance(config.getURL()));

        return connector.createEvent(0, ConnectorConstants.INCIDENT_DISC, json.toString(),
                ActionUtils.getSourceURI(source));
    }

    protected void setTestMode(boolean testMode) {
        this.testMode = testMode;
    }

    protected void setNumPages(int numPages) {
        this.numPages = numPages;
    }
}
