/*
 *
 * IBM Confidential
 * OCO Source Materials
 *
 * 5737-M96
 * (C) Copyright IBM Corp. 2022, 2023 All Rights Reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U.S. Copyright Office.
 *
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 *
 */
package com.ibm.watson.aiops.connectors.snow;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;

public class SNowResource {

    public final JsonNode result;
    public final String error;

    public SNowResource(@JsonProperty("result") JsonNode result, @JsonProperty("error") String error) {

        this.result = result;
        this.error = error;
    }
}