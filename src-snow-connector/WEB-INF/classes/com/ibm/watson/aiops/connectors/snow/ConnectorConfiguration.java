/*
 *
 * IBM Confidential
 * OCO Source Materials
 *
 * 5737-M96
 * (C) Copyright IBM Corp. 2022, 2024 All Rights Reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U.S. Copyright Office.
 *
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 *
 */
package com.ibm.watson.aiops.connectors.snow;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.logging.Level;
import java.util.logging.Logger;

import java.io.File;
import java.nio.file.Files;
import java.security.PrivateKey;
import java.util.Base64;
import javax.crypto.Cipher;
import io.fusionauth.pem.domain.PEM;

public class ConnectorConfiguration {

    // Required cloud event attributes
    public static final String PROPERTY_DATA = "data";
    public static final String PROPERTY_CONNECTION_ID = "connectionid";
    public static final String PROPERTY_COMPONENT_NAME = "componentname";
    public static final String PROPERTY_CONNECTION_MODE = "connection_mode";
    protected String connectionid;
    protected String componentname;
    // Default to live data if not provided
    protected String connmode = "live";

    public static final int DEFAULT_INT = -1;

    public static final String PROPERTY_CONNECTION_CONFIG = "connection_config";
    public static final String PROPERTY_URL = "url";
    public static final String PROPERTY_TYPES = "types";
    public static final String PROPERTY_USERNAME = "username";
    public static final String PROPERTY_PASSWORD = "password";
    public static final String PROPERTY_DATA_CONNECTION_ID = "connection_id";
    public static final String PROPERTY_MAPPING = "mapping";
    public static final String PROPERTY_CODEC = "codec";
    public static final String PROPERTY_DATASOURCE_TYPE = "datasource_type";
    public static final String PROPERTY_REQUEST_ACTION = "request_action";
    public static final String PROPERTY_HISTORICAL_CONFIG = "historical_config";
    public static final String PROPERTY_COLLECTION_MODE = "collection_mode";
    public static final String PROPERTY_START_DATE = "start_date";
    public static final String PROPERTY_END_DATE = "end_date";
    public static final String PROPERTY_SCHEDULE = "schedule";
    public static final String PROPERTY_INTERVAL = "interval";
    public static final String PROPERTY_UNITS = "units";

    // 4.6.0 new schema fields
    public static final String PROPERTY_SIMILAR_INCIDENT_POLLING_TOGGLE = "similar_incident_polling_toggle";
    public static final String PROPERTY_SIMILAR_INCIDENT = "similar_incident";
    public static final String PROPERTY_INCIDENT_SYNC_POLLING_TOGGLE = "incident_sync_polling_toggle";
    public static final String PROPERTY_INCIDENT_SYNC = "incident_sync";
    public static final String PROPERTY_ALERT_POLLING_TOGGLE = "alert_polling_toggle";
    public static final String PROPERTY_ALERT_POLLING = "alerts";
    public static final String PROPERTY_CHANGE_REQUEST_POLLING_TOGGLE = "change_request_polling_toggle";
    public static final String PROPERTY_CHANGE_REQUEST = "change_request";
    public static final String PROPERTY_POLLING_FIELD = "polling";
    public static final String PROPERTY_POLLING_UNIT_FIELD = "polling_unit";
    public static final String TOGGLE_ON = "on";
    public static final String TOGGLE_OFF_STRING = "off";

    public static final String PROPERTY_TLS = "tls";
    public static final String PROPERTY_CERTIFICATE = "ssl_verify_certificate";
    public static final String PROPERTY_USING_PROXY = "using_proxy";
    public static final String PROPERTY_PROXY_URL = "proxy_url";
    public static final String PROPERTY_PROXY_PORT = "tickets_proxy_port";
    public static final String PROPERTY_PROXY_USERNAME = "proxy_username";
    public static final String PROPERTY_PROXY_PASSWORD = "proxy_password";

    protected String types;
    protected String url;
    protected String username;
    protected String password;

    protected String dataConnectionId;

    protected String codec;

    protected String datasourceType;

    protected String requestAction;

    protected String collectionMode;
    protected String startDate;
    protected String endDate;

    // SSL Certificate
    private boolean tls = false;
    private String certificate;

    // Proxy Info
    private boolean usingProxy = false;
    private String proxyUrl;
    private Integer proxyPort;
    private String proxyUsername;
    private String proxyPassword;

    // default values for polling intervals
    protected boolean similarIncidentToggle = false;
    protected int similarIncidentInterval = 15;
    protected String similarIncidentIntervalUnit = ConnectorConstants.MINUTES;

    protected boolean changeRequestToggle = false;
    protected int changeRequestInterval = 15;
    protected String changeRequestIntervalUnit = ConnectorConstants.MINUTES;

    protected boolean incidentSyncToggle = false;
    protected int incidentSyncInterval = 1;
    protected String incidentSyncIntervalUnit = ConnectorConstants.MINUTES;

    protected boolean alertSyncToggle = false;
    protected int alertSyncInterval = 1;
    protected String alertSyncIntervalUnit = ConnectorConstants.MINUTES;

    protected Map<String, String> mapping;

    protected String timeZone;

    protected String passwordDecodeCertLocation = ConnectorConstants.DEFAULT_PASSWORD_DECODE_CERT_PATH;

    public static final String PROPERTY_RANGE = "range";
    public static final String PROPERTY_SIZE = "size";
    public static final String PROPERTY_KIND = "kind";
    public static final String PROPERTY_SNOW_PAYLOAD = "snow_payload";
    public static final String PROPERTY_STORY_ID = "story_id";
    public static final String PROPERTY_INCIDENT_ID = "incident_id";
    public static final String PROPERTY_SYS_ID = "sys_id";
    public static final String PROPERTY_BODY = "body";
    public String range;
    public int size;
    public String ce_conn_mode;

    static final Logger logger = Logger.getLogger(ConnectorConfiguration.class.getName());

    protected long startTime = System.nanoTime();

    public long getStartTime() {
        return startTime;
    }

    public void loadDataFromJson(String configurationJson) {

        JSONObject json = new JSONObject(configurationJson);

        try {
            // These cloud event attributes are guaranteed to exist
            connectionid = json.getString(PROPERTY_CONNECTION_ID);
            componentname = json.getString(PROPERTY_COMPONENT_NAME);
            if (json.has(PROPERTY_COLLECTION_MODE))
                connmode = json.getString(PROPERTY_COLLECTION_MODE);

            JSONObject dataJson = json.getJSONObject(PROPERTY_DATA);

            if (dataJson.has(PROPERTY_REQUEST_ACTION))
                requestAction = dataJson.getString(PROPERTY_REQUEST_ACTION);

            if (dataJson.has(PROPERTY_HISTORICAL_CONFIG)) {
                connmode = ConnectorConstants.HISTORICAL;
                JSONObject historicalConfig = dataJson.getJSONObject(PROPERTY_HISTORICAL_CONFIG);
                if (historicalConfig.has(PROPERTY_START_DATE))
                    startDate = historicalConfig.getString(PROPERTY_START_DATE);
                if (historicalConfig.has(PROPERTY_END_DATE))
                    endDate = historicalConfig.getString(PROPERTY_END_DATE);
            }

            if (dataJson.has(PROPERTY_CONNECTION_CONFIG)) {
                JSONObject connectionConfigJson = dataJson.getJSONObject(PROPERTY_CONNECTION_CONFIG);
                if (connectionConfigJson.has(PROPERTY_TYPES))
                    types = connectionConfigJson.getString(PROPERTY_TYPES);
                if (connectionConfigJson.has(PROPERTY_URL))
                    url = connectionConfigJson.getString(PROPERTY_URL);
                if (connectionConfigJson.has(PROPERTY_USERNAME))
                    username = connectionConfigJson.getString(PROPERTY_USERNAME);
                if (connectionConfigJson.has(PROPERTY_PASSWORD)) {
                    extractPassword(connectionConfigJson.getString(PROPERTY_PASSWORD));
                }

                // change risk polling details
                if (connectionConfigJson.has(PROPERTY_CHANGE_REQUEST_POLLING_TOGGLE)
                        && connectionConfigJson.getString(PROPERTY_CHANGE_REQUEST_POLLING_TOGGLE).equals(TOGGLE_ON)) {
                    changeRequestToggle = true;
                    JSONObject crJson = connectionConfigJson.getJSONObject(PROPERTY_CHANGE_REQUEST);
                    changeRequestInterval = crJson.getInt(PROPERTY_POLLING_FIELD);
                    changeRequestIntervalUnit = crJson.getString(PROPERTY_POLLING_UNIT_FIELD);
                }
                // alert sync polling details
                if (connectionConfigJson.has(PROPERTY_ALERT_POLLING_TOGGLE)
                        && connectionConfigJson.getString(PROPERTY_ALERT_POLLING_TOGGLE).equals(TOGGLE_ON)) {
                    alertSyncToggle = true;
                    JSONObject alertJson = connectionConfigJson.getJSONObject(PROPERTY_ALERT_POLLING);
                    alertSyncInterval = alertJson.getInt(PROPERTY_POLLING_FIELD);
                    alertSyncIntervalUnit = alertJson.getString(PROPERTY_POLLING_UNIT_FIELD);
                }
                // incident sync polling details
                if (connectionConfigJson.has(PROPERTY_INCIDENT_SYNC_POLLING_TOGGLE)
                        && connectionConfigJson.getString(PROPERTY_INCIDENT_SYNC_POLLING_TOGGLE).equals(TOGGLE_ON)) {
                    incidentSyncToggle = true;
                    JSONObject incidentJson = connectionConfigJson.getJSONObject(PROPERTY_INCIDENT_SYNC);
                    incidentSyncInterval = incidentJson.getInt(PROPERTY_POLLING_FIELD);
                    incidentSyncIntervalUnit = incidentJson.getString(PROPERTY_POLLING_UNIT_FIELD);
                }
                // similar incident polling details
                if (connectionConfigJson.has(PROPERTY_SIMILAR_INCIDENT_POLLING_TOGGLE)
                        && connectionConfigJson.getString(PROPERTY_SIMILAR_INCIDENT_POLLING_TOGGLE).equals(TOGGLE_ON)) {
                    similarIncidentToggle = true;
                    JSONObject simJson = connectionConfigJson.getJSONObject(PROPERTY_SIMILAR_INCIDENT);
                    similarIncidentInterval = simJson.getInt(PROPERTY_POLLING_FIELD);
                    similarIncidentIntervalUnit = simJson.getString(PROPERTY_POLLING_UNIT_FIELD);
                }

                // tls/ssl certificate

                if (connectionConfigJson.has(PROPERTY_TLS)) {
                    tls = connectionConfigJson.getBoolean(PROPERTY_TLS);

                    if (tls) {
                        if (connectionConfigJson.has(PROPERTY_CERTIFICATE)) {
                            certificate = connectionConfigJson.getString(PROPERTY_CERTIFICATE);
                        }
                    }
                }

                // Proxy Information
                if (connectionConfigJson.has(PROPERTY_USING_PROXY)) {
                    usingProxy = connectionConfigJson.getBoolean(PROPERTY_USING_PROXY);

                    if (usingProxy) {
                        if (connectionConfigJson.has(PROPERTY_PROXY_URL)) {
                            proxyUrl = connectionConfigJson.getString(PROPERTY_PROXY_URL);
                        }

                        if (connectionConfigJson.has(PROPERTY_PROXY_PORT)) {
                            proxyPort = connectionConfigJson.getInt(PROPERTY_PROXY_PORT);
                        }

                        if (connectionConfigJson.has(PROPERTY_PROXY_USERNAME)) {
                            proxyUsername = connectionConfigJson.getString(PROPERTY_PROXY_USERNAME);
                        }

                        if (connectionConfigJson.has(PROPERTY_PROXY_PASSWORD)) {
                            proxyPassword = connectionConfigJson.getString(PROPERTY_PROXY_PASSWORD);
                        }
                    }
                }
            }

            if (dataJson.has(PROPERTY_MAPPING)) {
                JSONObject mapJson = dataJson.getJSONObject(PROPERTY_MAPPING);
                mapping = new HashMap<String, String>();
                Iterator<String> keys = mapJson.keys();
                while (keys.hasNext()) {
                    String key = keys.next();
                    String value = mapJson.get(key).toString();
                    mapping.put(key, value);
                }
            }
        } catch (Exception e) {
            logger.log(Level.SEVERE, e.getMessage(), e);
        }
        // logger.log(Level.INFO, toString());
    }

    private void extractPassword(String pwd) {
        password = pwd;
        if (password.startsWith(ConnectorConstants.ENCRYPTED_PREFIX)) {
            logger.log(Level.INFO, "Password is encrypted");
            // Remove the encrypt part
            password = password.substring(ConnectorConstants.ENCRYPTED_PREFIX.length());

            String tlsKeyPath = System.getProperty(ConnectorConstants.ENV_JWT_CERT_PATH);
            String fileContent = "";
            File jwtCertFile = null;

            if (tlsKeyPath != null && tlsKeyPath.length() > 0) {
                logger.log(Level.INFO, "Found environment variable with path to TLS " + tlsKeyPath);
                jwtCertFile = new File(tlsKeyPath);
            } else {
                logger.log(Level.INFO, "Using default location " + getPasswordDecodeCertLocation());
                // Try to read from default cert location
                // /bindings/grpc-bridge/tls.key
                jwtCertFile = new File(getPasswordDecodeCertLocation());
            }
            try {
                fileContent = Files.readString(jwtCertFile.toPath());

                PrivateKey privateKey = PEM.decode(fileContent).privateKey;
                Cipher cipher = Cipher.getInstance("RSA");
                cipher.init(Cipher.DECRYPT_MODE, privateKey);

                password = new String(cipher.doFinal(Base64.getDecoder().decode(password)));
                logger.log(Level.INFO, "Successfully decrpyted password");
            } catch (Exception e) {
                // On error et it to null
                password = null;
                logger.log(Level.INFO, "Failed to decrypt password " + e.getMessage());
            }
        }
    }

    public String toString() {
        StringBuffer sb = new StringBuffer();
        sb.append(PROPERTY_CONNECTION_ID + ": " + connectionid + "\n");
        sb.append(PROPERTY_COMPONENT_NAME + ": " + componentname + "\n");
        sb.append(PROPERTY_URL + ": " + url + "\n");
        // Showing username for debugging purposes
        sb.append(PROPERTY_USERNAME + ": " + username + "\n");
        // Hide the password due to security reasons
        sb.append(PROPERTY_PASSWORD + ": is the password null? : " + (password == null) + "\n");

        sb.append(PROPERTY_REQUEST_ACTION + ": " + requestAction + "\n");

        sb.append(PROPERTY_MAPPING + ": " + mapping + "\n");
        sb.append(PROPERTY_TYPES + ": " + types + "\n");

        sb.append(PROPERTY_CHANGE_REQUEST_POLLING_TOGGLE + ": " + changeRequestToggle + " changeRequestInterval: "
                + changeRequestInterval + " changeRequestIntervalUnit: " + changeRequestIntervalUnit + "\n");
        sb.append(PROPERTY_ALERT_POLLING_TOGGLE + ": " + alertSyncToggle + " alertSyncInterval: " + alertSyncInterval
                + " alertSyncIntervalUnit: " + alertSyncIntervalUnit + "\n");
        sb.append(PROPERTY_INCIDENT_SYNC + ": " + incidentSyncToggle + " incidentSyncInterval: " + incidentSyncInterval
                + " incidentSyncIntervalUnit: " + incidentSyncIntervalUnit + "\n");
        sb.append(PROPERTY_SIMILAR_INCIDENT_POLLING_TOGGLE + ": " + similarIncidentToggle + " similarIncidentInterval: "
                + similarIncidentInterval + " similarIncidentIntervalUnit: " + similarIncidentIntervalUnit + "\n");

        sb.append(PROPERTY_TLS + ": " + tls + "\n");
        sb.append(PROPERTY_USING_PROXY + ": " + usingProxy + "\n");

        if (this.usingProxy) {
            sb.append(PROPERTY_PROXY_URL + ": " + proxyUrl + "\n");
            sb.append(PROPERTY_PROXY_PORT + ": " + proxyPort + "\n");
        }

        return sb.toString();
    }

    public String getConnectionID() {
        return connectionid;
    }

    public String getComponentName() {
        return componentname;
    }

    public String getConnMode() {
        return connmode;
    }

    public void setConnMode(String connmode) {
        this.connmode = connmode;
    }

    public String getURL() {
        return url;
    }

    public void setURL(String url) {
        this.url = url;
    }

    public String getTypes() {
        return types;
    }

    public void setTypes(String types) {
        this.types = types;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getRequestAction() {
        return requestAction;
    }

    public void setRequestAction(String requestAction) {
        this.requestAction = requestAction;
    }

    public String getStartdate() {
        return startDate;
    }

    public void setStartdate(String startDate) {
        this.startDate = startDate;
    }

    public String getEnddate() {
        return endDate;
    }

    public void setEnddate(String endDate) {
        this.endDate = endDate;
    }

    public String getTimeZone() {
        return timeZone;
    }

    public void setTimeZone(String timeZone) {
        this.timeZone = timeZone;
    }

    public Map<String, String> getMapping() {
        return mapping;
    }

    public void setMapping(Map<String, String> mapping) {
        this.mapping = mapping;
    }

    protected String getPasswordDecodeCertLocation() {
        return passwordDecodeCertLocation;
    }

    protected void setPasswordDecodeCertLocation(String passwordDecodeCertLocation) {
        this.passwordDecodeCertLocation = passwordDecodeCertLocation;
    }

    public boolean getSimilarIncidentToggle() {
        return similarIncidentToggle;
    }

    public int getSimilarIncidentInterval() {
        return similarIncidentInterval;
    }

    public String getSimilarIncidentIntervalUnit() {
        return similarIncidentIntervalUnit;
    }

    public boolean getChangeRequestToggle() {
        return changeRequestToggle;
    }

    public int getChangeRequestInterval() {
        return changeRequestInterval;
    }

    public String getChangeRequestIntervalUnit() {
        return changeRequestIntervalUnit;
    }

    public boolean getIncidentSyncToggle() {
        return incidentSyncToggle;
    }

    public int getIncidentSyncInterval() {
        return incidentSyncInterval;
    }

    public String getIncidentSyncIntervalUnit() {
        return incidentSyncIntervalUnit;
    }

    public boolean getAlertSyncToggle() {
        return alertSyncToggle;
    }

    public int getAlertSyncInterval() {
        return alertSyncInterval;
    }

    public String getAlertSyncIntervalUnit() {
        return alertSyncIntervalUnit;
    }

    public boolean getTls() {
        return tls;
    }

    public void setTls(boolean tls) {
        this.tls = tls;
    }

    public String getCertificate() {
        return certificate;
    }

    public void setCertificate(String certificate) {
        this.certificate = certificate;
    }

    public void setIsProxyEnabled(Boolean usingProxy) {
        this.usingProxy = usingProxy;
    }

    public boolean getIsProxyEnabled() {
        return this.usingProxy;
    }

    public void setProxyUrl(String proxyUrl) {
        this.proxyUrl = proxyUrl;
    }

    public String getProxyUrl() {
        return this.proxyUrl;
    }

    public void setProxyPort(Integer proxyPort) {
        this.proxyPort = proxyPort;
    }

    public Integer getProxyPort() {
        return this.proxyPort;
    }

    public void setProxyUsername(String proxyUsername) {
        this.proxyUsername = proxyUsername;
    }

    public String getProxyUsername() {
        return this.proxyUsername;
    }

    public void setProxyPassword(String proxyPassword) {
        this.proxyPassword = proxyPassword;
    }

    public String getProxyPassword() {
        return this.proxyPassword;
    }
}
