/*
 *
 * IBM Confidential
 * OCO Source Materials
 *
 * 5737-M96
 * (C) Copyright IBM Corp. 2022, 2023 All Rights Reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U.S. Copyright Office.
 *
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 *
 */
package com.ibm.watson.aiops.connectors.snow;

import java.net.http.HttpResponse;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.logging.Level;
import java.util.logging.Logger;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import io.cloudevents.CloudEvent;
import io.micrometer.core.instrument.Counter;

public class ChangeRequestPollAction implements Runnable {

    private Counter actionCounter;
    private Counter actionErrorCounter;
    private ConnectorConfiguration config;
    private String connMode;
    private String username;
    private HttpClientUtil httpClient;

    private ScheduledExecutorService executorService = null;

    private Connector connector;
    private CloudEvent ce;

    private int offset = 0;

    static final Logger logger = Logger.getLogger(ChangeRequestPollAction.class.getName());

    private AtomicBoolean stopDataCollection = new AtomicBoolean(false);

    public ChangeRequestPollAction(ConnectorAction action) {
        actionCounter = action.getActionCounter();
        actionErrorCounter = action.getActionErrorCounter();

        config = action.getConfiguration();
        connMode = config.getConnMode();
        username = config.getUsername();

        httpClient = action.getHttpClient();
        connector = action.getConnector();

        actionCounter = action.getActionCounter();
        actionErrorCounter = action.getActionErrorCounter();

    }

    @Override
    public void run() {
        logger.log(Level.INFO, "Run ChangeRequest Poll Action");
        actionCounter.increment();

        if (connMode.equals(ConnectorConstants.HISTORICAL)) {
            logger.log(Level.INFO, "Start collecting historical data");
            fetchAndEmitChangeRequest();
        } else {
            // Create a single-threaded executor
            executorService = Executors.newSingleThreadScheduledExecutor();
            int interval = config.getChangeRequestInterval();
            TimeUnit unit = ActionUtils.convertToTimeUnit(config.getChangeRequestIntervalUnit());
            logger.log(Level.INFO, "fetchAndEmitChangeRequest with interval: " + interval + " and unit: " + unit);
            executorService.scheduleAtFixedRate(this::fetchAndEmitChangeRequest, 0, interval, unit);
        }
    }

    public void stop() {
        logger.log(Level.INFO, "stop(): change request stop called");
        // This variable needs to be set as the shutdown of the executorService
        // will not force stop the polling from stopping
        stopDataCollection.set(true);

        if (executorService != null) {
            logger.log(Level.INFO, "stop(): Stopping changeRequest polling thread");
            executorService.shutdownNow();
            logger.log(Level.INFO, "ChangeRequest polling stopped");
        }
    }

    private void fetchAndEmitChangeRequest() {
        logger.log(Level.INFO, "Calling fetchAndEmitChangeRequest");
        String path;
        String range = ActionUtils.getRange(config, ConnectorConfiguration.PROPERTY_CHANGE_REQUEST);
        String bodyString = "";
        JSONObject body;
        JSONArray result;
        CompletableFuture<HttpResponse<String>> cFResp;
        HttpResponse<String> res;

        int changeRequestCounter = 0;

        boolean hasNextPage = true;
        // Reset the offset so subsequent calls start at the start
        offset = 0;
        while (hasNextPage) {
            try {
                if (stopDataCollection.get()) {
                    logger.log(Level.INFO, "Data flow changed to stopped, terminating data gathering");
                    break;
                }

                path = ConnectorConstants.CHANGE_REQUEST_QUERY_API_PATH + ConnectorConstants.QUERY_GENERAL_PROPERTIES
                        + ConnectorConstants.QUERY_OFFSET + offset + ConnectorConstants.QUERY_PAGING
                        + ConnectorConstants.QUERY_UPDATED_BETWEEN + range;

                if (!connMode.equals(ConnectorConstants.HISTORICAL)) {
                    path += ConnectorConstants.QUERY_EXCLUDE_USER + ActionUtils.encodeString(username);
                }

                logger.log(Level.INFO, "Query path: " + path);

                cFResp = httpClient.get(path);
                res = cFResp.get();
                bodyString = res.body();
                body = new JSONObject(bodyString.trim());
                result = body.getJSONArray("result");

                for (int i = 0; i < result.length(); i++) {
                    if (stopDataCollection.get()) {
                        logger.log(Level.INFO, "Data flow changed to stopped, terminating data gathering");
                        break;
                    }
                    ce = getChangeRequestCE(result.getJSONObject(i));
                    connector.emitCloudEvent(ConnectorConstants.TOPIC_OUTPUT_CHANGE_REQUEST, connector.getPartition(),
                            ce);
                    changeRequestCounter++;
                }

                if (result.length() == 0) {
                    hasNextPage = false;
                }

                offset += result.length();

            } catch (InterruptedException e) {
                logger.log(Level.INFO, "Interrupt caused by a valid stop, polling stopped");
                break;
            } catch (Exception e) {
                actionErrorCounter.increment();
                logger.log(Level.SEVERE, "Could not get response ", e);
                connector.triggerAlerts(config, e, bodyString);
            }
        }
        logger.log(Level.INFO, "Number of change requests found: " + changeRequestCounter);
        connector.clearAlerts(config, ConnectorConstants.ALL_ALERTS_MINUS_IBM_APP, connector.getTimeLastCleared());

    }

    private CloudEvent getChangeRequestCE(JSONObject changeRequest) {
        JSONObject json = new JSONObject();
        String[] keys = { ConnectorConstants.SYS_ID, ConnectorConstants.NUMBER, "u_environment", "u_environment_id",
                "cmdb_ci", "short_description", "u_purpose", "description", "backout_plan", "close_notes", "state",
                "close_category", "sys_created_by", "assigned_to", "sys_domain", "category", "business_service",
                "priority", "approval", "type", "contact_type", "production_system", "impact", "reason",
                "justification", "start_date", "end_date", "work_start", "work_end", "calendar_duration", "close_code",
                "delivery_task", "implementation_plan", "test_plan", "work_notes_list" };

        for (String key : keys) {
            if (changeRequest.has(key)) {
                Object obj = changeRequest.get(key);
                json.put(key, obj.toString());
            }
        }

        String workNotes = "";
        if (changeRequest.has(ConnectorConstants.WORK_NOTES)) {
            workNotes = changeRequest.getString(ConnectorConstants.WORK_NOTES);
        }
        String[] workNotesArr = workNotes.split("\\n\\n");
        json.put(ConnectorConstants.WORK_NOTES, workNotesArr);
        ActionUtils.applyMappings(changeRequest, json, config.getMapping());
        json.put(ConnectorConstants.RAW_DATA, changeRequest);

        String source = null;
        // The reason why the number may be null is from our auto tests
        try {
            Object numberObj = json.get(ConnectorConstants.NUMBER);
            if (numberObj != null) {
                source = ActionUtils.getSourceIdentifier(config.getURL(),
                        ConnectorConstants.SOURCE_IDENTIFIER_CHANGE_REQUEST, numberObj.toString());
            }
        } catch (JSONException je) {
            // Do nothing, the number should normally be a required field
        }

        if (connMode.equals(ConnectorConstants.HISTORICAL)) {
            json.put(ConnectorConstants.CE_CONNECTION_MODE, ConnectorConstants.HISTORICAL);
        } else {
            json.put(ConnectorConstants.CE_CONNECTION_MODE, ConnectorConstants.LIVE);
        }

        json.put(ConnectorConstants.CONNECTION_ID, config.getConnectionID());
        json.put(ConnectorConstants.INSTANCE, ActionUtils.getInstance(config.getURL()));

        return connector.createEvent(0, ConnectorConstants.CHANGE_REQUEST_DISC, json.toString(),
                ActionUtils.getSourceURI(source));
    }
}
