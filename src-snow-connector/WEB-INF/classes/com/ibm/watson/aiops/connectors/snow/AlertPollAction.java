/*
*
* IBM Confidential
* OCO Source Materials
*
* 5737-M96
* (C) Copyright IBM Corp. 2022, 2024 All Rights Reserved.
*
* The source code for this program is not published or otherwise
* divested of its trade secrets, irrespective of what has been
* deposited with the U.S. Copyright Office.
*
* US Government Users Restricted Rights - Use, duplication or
* disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
*
*/
package com.ibm.watson.aiops.connectors.snow;

import java.net.http.HttpResponse;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.logging.Level;
import java.util.logging.Logger;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import io.cloudevents.CloudEvent;
import io.micrometer.core.instrument.Counter;

public class AlertPollAction implements Runnable {

    private Counter actionCounter;
    private Counter actionErrorCounter;
    private ConnectorConfiguration config;
    private String connMode;
    private String username;
    private HttpClientUtil httpClient;

    private ScheduledExecutorService executorService = null;

    private Connector connector;
    private CloudEvent ce;

    private int offset = 0;

    private boolean testMode = false;
    private int numPages = 1;

    static final Logger logger = Logger.getLogger(AlertPollAction.class.getName());

    private AtomicBoolean stopDataCollection = new AtomicBoolean(false);

    public AlertPollAction(ConnectorAction action) {

        actionCounter = action.getActionCounter();
        actionErrorCounter = action.getActionErrorCounter();

        config = action.getConfiguration();
        connMode = config.getConnMode();
        username = config.getUsername();

        httpClient = action.getHttpClient();
        connector = action.getConnector();

        actionCounter = action.getActionCounter();
        actionErrorCounter = action.getActionErrorCounter();

    }

    @Override
    public void run() {
        logger.log(Level.INFO, "Run Alert Poll Action");
        actionCounter.increment();
        TimeUnit unit = ActionUtils.convertToTimeUnit(config.getAlertSyncIntervalUnit());
        int interval = config.getAlertSyncInterval();

        // Create a single-threaded executor
        executorService = Executors.newSingleThreadScheduledExecutor();
        logger.log(Level.INFO, "fetchAndEmitAlert with interval: " + interval + " and unit: " + unit);
        executorService.scheduleAtFixedRate(this::fetchAndEmitAlert, 0, interval, unit);

    }

    public void stop() {
        logger.log(Level.INFO, "stop(): alert stop called");
        // This variable needs to be set as the shutdown of the executorService
        // will not force stop the polling from stopping
        stopDataCollection.set(true);

        if (executorService != null) {
            logger.log(Level.INFO, "stop(): Stopping alert polling thread");
            executorService.shutdownNow();
            logger.log(Level.INFO, "Alert polling stopped");
        }
    }

    private void fetchAndEmitAlert() {
        logger.log(Level.INFO, "Calling fetchAndEmitAlert");
        String path;
        String range = ActionUtils.getRange(config, ConnectorConfiguration.PROPERTY_ALERT_POLLING);
        String bodyString = "";
        JSONObject body;
        JSONArray result;
        CompletableFuture<HttpResponse<String>> cFResp;
        HttpResponse<String> res;

        boolean hasNextPage = true;
        int pageCounter = 0;

        int alertCounter = 0;

        // Reset the offset so subsequent calls start at the start
        offset = 0;
        while (hasNextPage) {
            try {
                if (stopDataCollection.get()) {
                    logger.log(Level.INFO, "Data flow changed to stopped, terminating data gathering");
                    break;
                }

                path = ConnectorConstants.SNOW_ALERT_API_PATH + ConnectorConstants.QUERY_GENERAL_PROPERTIES
                        + ConnectorConstants.QUERY_OFFSET + offset + ConnectorConstants.QUERY_PAGING
                        + ConnectorConstants.QUERY_UPDATED_BETWEEN + range;

                if (!connMode.equals(ConnectorConstants.HISTORICAL)) {
                    path += ConnectorConstants.QUERY_EXCLUDE_USER + ActionUtils.encodeString(username);
                }

                logger.log(Level.INFO, "Query path: " + path);

                cFResp = httpClient.get(path);
                res = cFResp.get();
                bodyString = res.body();
                body = new JSONObject(bodyString.trim());
                result = body.getJSONArray("result");

                for (int i = 0; i < result.length(); i++) {
                    if (stopDataCollection.get()) {
                        logger.log(Level.INFO, "Data flow changed to stopped, terminating data gathering");
                        break;
                    }
                    ce = getAlertCE(result.getJSONObject(i));
                    connector.emitCloudEvent(ConnectorConstants.TOPIC_OUTPUT_SNOW_ALERT, connector.getPartition(), ce);
                    alertCounter++;
                }

                if (result.length() == 0) {
                    hasNextPage = false;
                }

                offset += result.length();

            } catch (InterruptedException e) {
                logger.log(Level.INFO, "Interrupt caused by a valid stop, polling stopped");
                break;
            } catch (Exception e) {
                actionErrorCounter.increment();
                logger.log(Level.SEVERE, "Could not get response ", e);
                connector.triggerAlerts(config, e, bodyString);
            }
            pageCounter++;
            if (testMode && numPages >= pageCounter) {
                break;
            }
        }

        logger.log(Level.INFO, "Number of alerts found: " + alertCounter);
        connector.clearAlerts(config, ConnectorConstants.ALL_ALERTS_MINUS_IBM_APP, connector.getTimeLastCleared());

    }

    private CloudEvent getAlertCE(JSONObject alert) {
        JSONObject json = new JSONObject();
        String[] keys = { ConnectorConstants.SYS_ID, ConnectorConstants.NUMBER, "severity", "resource", "event_count",
                "target_url", "sys_mod_count", "created_at", "sys_updated_on", "sys_tags", "type",
                "first_occurrence_timestamp", "deduplication_key", "sys_id", "sys_updated_by", "updated_at",
                "sys_created_on", "last_occurrence_timestamp", "name", "details", "id", "status_timestamp", "incident",
                "sys_created_by", "status" };

        for (String key : keys) {
            if (alert.has(key)) {
                Object obj = alert.get(key);
                json.put(key, obj.toString());
            }
        }

        json.put("source_name", "service now");
        ActionUtils.applyMappings(alert, json, config.getMapping());

        String source = null;
        // The reason why the number may be null is from our auto tests
        try {
            Object numberObj = json.get(ConnectorConstants.NUMBER);
            if (numberObj != null) {
                source = ActionUtils.getSourceIdentifier(config.getURL(), ConnectorConstants.SOURCE_IDENTIFIER_INCIDENT,
                        numberObj.toString());
            }
        } catch (JSONException je) {
            // Do nothing, the number should normally be a required field
        }

        json.put(ConnectorConstants.CONNECTION_ID, config.getConnectionID());
        json.put(ConnectorConstants.INSTANCE, ActionUtils.getInstance(config.getURL()));

        return connector.createEvent(0, ConnectorConstants.ALERT_DISC, json.toString(),
                ActionUtils.getSourceURI(source));
    }

    protected void setTestMode(boolean testMode) {
        this.testMode = testMode;
    }

    protected void setNumPages(int numPages) {
        this.numPages = numPages;
    }
}