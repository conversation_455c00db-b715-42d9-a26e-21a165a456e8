/*
 *
 * IBM Confidential
 * OCO Source Materials
 *
 * 5737-M96
 * (C) Copyright IBM Corp. 2022, 2023 All Rights Reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U.S. Copyright Office.
 *
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 *
 */
package com.ibm.watson.aiops.connectors.snow;

import java.util.logging.Level;
import java.util.logging.Logger;

import com.ibm.cp4waiops.connectors.sdk.ConnectorManager;
import com.ibm.cp4waiops.connectors.sdk.SDKCheck;
import com.ibm.cp4waiops.connectors.sdk.StandardConnectorManager;

import jakarta.inject.Inject;
import jakarta.servlet.ServletContextEvent;
import jakarta.servlet.ServletContextListener;
import jakarta.servlet.annotation.WebListener;

@WebListener
public class SNowContextListener implements ServletContextListener {
    static final Logger logger = Logger.getLogger(SNowContextListener.class.getName());

    @Inject
    ManagerInstance instance;

    private ConnectorManager manager = new StandardConnectorManager(new ConnectorFactory());

    @Override
    public void contextInitialized(ServletContextEvent sce) {
        logger.log(Level.INFO, "connector manager initializing");
        ConnectorManager manager = instance.getConnectorManager();
        manager.init();
        SDKCheck.setInstance(manager);
    }

    @Override
    public void contextDestroyed(ServletContextEvent sce) {
        logger.log(Level.INFO, "template servlet destroying");
        ConnectorManager manager = instance.getConnectorManager();
        SDKCheck.removeInstance(manager);
        manager.destroy();
    }
}