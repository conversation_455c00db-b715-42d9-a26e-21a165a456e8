/*
 *
 * IBM Confidential
 * OCO Source Materials
 *
 * 5737-M96
 * (C) Copyright IBM Corp. 2022, 2023 All Rights Reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U.S. Copyright Office.
 *
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 *
 */
package com.ibm.watson.aiops.connectors.snow;

import com.ibm.cp4waiops.connectors.sdk.ConnectorManager;
import com.ibm.cp4waiops.connectors.sdk.StandardConnectorManager;

@jakarta.enterprise.context.ApplicationScoped
public class ManagerInstance {
    private ConnectorManager manager = new StandardConnectorManager(new ConnectorFactory());

    public ConnectorManager getConnectorManager() {
        return manager;
    }
}
