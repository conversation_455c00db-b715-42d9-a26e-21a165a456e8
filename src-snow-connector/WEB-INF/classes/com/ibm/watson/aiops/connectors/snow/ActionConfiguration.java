/*
 *
 * IBM Confidential
 * OCO Source Materials
 *
 * 5737-M96
 * (C) Copyright IBM Corp. 2022, 2023 All Rights Reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U.S. Copyright Office.
 *
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 *
 */
package com.ibm.watson.aiops.connectors.snow;

import org.json.JSONObject;

import java.util.logging.Level;
import java.util.logging.Logger;

public class ActionConfiguration {

    /*
     * Incidents creation sample: { "connection_id":"07-snow1", "story_id":"${07-snow4}", "incident_id":"INC0021355",
     * "snow_payload":{ <JSON data not shown here due to its large size> } } Comment creation sample: { "connection_id":
     * "05-snow2", "kind": "change_request", "sys_id": "a9e9c33dc61122760072455df62663d2", "body": "This is a comment" }
     */

    public static final String PROPERTY_DATA = "data";
    public static final String PROPERTY_CONNECTION_ID = "connectionid";
    public static final String PROPERTY_KIND = "kind";
    public static final String PROPERTY_SYS_ID = "sys_id";
    public static final String PROPERTY_SNOW_PAYLOAD = "snow_payload";
    public static final String PROPERTY_STORY_ID = "story_id";
    public static final String PROPERTY_ALERT_ID = "alert_id";
    public static final String PROPERTY_INCIDENT_ID = "incident_id";
    public static final String PROPERTY_BODY = "body";

    protected String connectionid;
    public String kind;
    public String sys_id;
    public String snow_payload;
    public String story_id;
    public String alert_id;
    public String incident_id;
    public String body;
    public String data;

    static final Logger logger = Logger.getLogger(ActionConfiguration.class.getName());

    public void loadDataFromJson(String actionProperties) {
        JSONObject json = new JSONObject(actionProperties);

        // These cloud event attributes are guaranteed to exist
        connectionid = json.getString(PROPERTY_CONNECTION_ID);

        try {

            JSONObject dataJson = json.getJSONObject(PROPERTY_DATA);

            data = dataJson.toString();

            if (dataJson.has(PROPERTY_KIND))
                kind = dataJson.getString(PROPERTY_KIND);
            if (dataJson.has(PROPERTY_SNOW_PAYLOAD))
                snow_payload = dataJson.get(PROPERTY_SNOW_PAYLOAD).toString();
            if (dataJson.has(PROPERTY_STORY_ID))
                story_id = dataJson.getString(PROPERTY_STORY_ID);
            if (dataJson.has(PROPERTY_ALERT_ID))
                alert_id = dataJson.getString(PROPERTY_ALERT_ID);
            if (dataJson.has(PROPERTY_INCIDENT_ID))
                incident_id = dataJson.getString(PROPERTY_INCIDENT_ID);
            if (dataJson.has(PROPERTY_SYS_ID))
                sys_id = dataJson.getString(PROPERTY_SYS_ID);
            if (dataJson.has(PROPERTY_BODY))
                body = dataJson.getString(PROPERTY_BODY);
        } catch (Exception e) {
            logger.log(Level.SEVERE, e.getMessage(), e);
        }
    }

    public String getSysID() {
        return sys_id;
    }

    public void setSysID(String sys_id) {
        this.sys_id = sys_id;
    }

    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }

    public String getKind() {
        return kind;
    }

    public void setKind(String kind) {
        this.kind = kind;
    }

    public String getSnowPayload() {
        return snow_payload;
    }

    public void setSnowPayload(String snow_payload) {
        this.snow_payload = snow_payload;
    }

    public String getStoryID() {
        return story_id;
    }

    public void setStoryID(String story_id) {
        this.story_id = story_id;
    }

    public String getAlertID() {
        return alert_id;
    }

    public void setAlertID(String alert_id) {
        this.alert_id = alert_id;
    }

    public String getIncidentID() {
        return incident_id;
    }

    public void setIncidentID(String incident_id) {
        this.incident_id = incident_id;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    public String toString() {
        StringBuffer sb = new StringBuffer();
        sb.append(PROPERTY_CONNECTION_ID + ": " + connectionid + "\n");
        sb.append(PROPERTY_SYS_ID + ": " + sys_id + "\n");
        sb.append(PROPERTY_BODY + ": " + body + "\n");
        sb.append(PROPERTY_KIND + ": " + kind + "\n");
        sb.append(PROPERTY_INCIDENT_ID + ": " + incident_id + "\n");
        sb.append(PROPERTY_STORY_ID + ": " + story_id + "\n");
        sb.append(PROPERTY_ALERT_ID + ": " + alert_id + "\n");
        sb.append(PROPERTY_DATA + ": " + data + "\n");

        return sb.toString();
    }
}