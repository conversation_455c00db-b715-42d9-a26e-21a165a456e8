/*
 *
 * IBM Confidential
 * OCO Source Materials
 *
 * 5737-M96
 * (C) Copyright IBM Corp. 2022, 2023 All Rights Reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the U.S. Copyright Office.
 *
 * US Government Users Restricted Rights - Use, duplication or
 * disclosure restricted by GSA ADP Schedule Contract with IBM Corp.
 *
 */
package com.ibm.watson.aiops.connectors.snow;

import java.net.URI;
import java.net.http.HttpResponse;
import java.util.concurrent.CompletableFuture;
import java.util.logging.Level;
import java.util.logging.Logger;

import org.json.JSONObject;

import io.cloudevents.CloudEvent;
import io.micrometer.core.instrument.Counter;

public class IncidentCreateAction implements Runnable {
    ConnectorAction action;

    static final Logger logger = Logger.getLogger(IncidentCreateAction.class.getName());

    public IncidentCreateAction(ConnectorAction action) {
        this.action = action;
    }

    public void run() {
        logger.log(Level.INFO, "Run Incident Create Action");

        Counter actionCounter = action.getActionCounter();
        Counter actionErrorCounter = action.getActionErrorCounter();

        actionCounter.increment();

        ConnectorConfiguration config = action.getConfiguration();
        ActionConfiguration actionConfig = action.getActionConfiguration();

        Connector connector = action.getConnector();

        CloudEvent ce;

        JSONObject json = new JSONObject();

        HttpClientUtil httpClient = action.getHttpClient();

        String path = ConnectorConstants.INCIDENT_CREATE_PATH;

        CompletableFuture<HttpResponse<String>> cFResp;
        HttpResponse<String> res;
        String bodyString = "";

        String storyId = actionConfig.getStoryID();
        String incidentId = actionConfig.getIncidentID();
        String snowPayload = actionConfig.getSnowPayload();

        try {
            if (incidentId == null || incidentId.isBlank()) {
                logger.log(Level.INFO, "Creating incident for story " + storyId);
                cFResp = httpClient.post(path, snowPayload);
            } else {
                logger.log(Level.INFO, "Updating incident " + incidentId + " for story " + storyId);
                path += "/" + incidentId;
                cFResp = httpClient.patch(path, snowPayload);
                json = json.put("incident_id", incidentId);
            }

            logger.log(Level.INFO, "Incident creation successful ");

            res = cFResp.get();
            bodyString = res.body();

            json.put("connection_id", config.getConnectionID());
            json.put("story_id", storyId);

            JSONObject bodyStringJSONObject = null;
            String source = null;
            // Example body string:
            // {"result":{"status":"success","incident":{"sysId":"27e201c62f21c1108bfb56e62799b687","incidentNumber":"INC0021357"},"message":"Successfully
            // created incident with id 27e201c62f21c1108bfb56e62799b687"}}
            logger.log(Level.INFO, "Response body " + bodyString);
            try {
                bodyStringJSONObject = new JSONObject(bodyString);
                String number = bodyStringJSONObject.getJSONObject("result").getJSONObject("incident")
                        .getString("sysId");
                source = ActionUtils.getSourceIdentifier(config.getURL(),
                        ConnectorConstants.SOURCE_IDENTIFIER_INCIDENT_CREATION, number);
                json.put("permalink", source);
            } catch (Exception e) {
                // Do nothing, URL cannot be built
                logger.log(Level.WARNING, "Incident creation URL cannot be created: " + e.getMessage());
                connector.triggerAlerts(config, e, bodyString);
            }

            if (bodyStringJSONObject != null) {
                // The snow_response is expected to be JSON and not a string escaped version of it
                json.put("snow_response", bodyStringJSONObject);
            }
            /*
             * Example json body: { "connection_id":"1b1aeb97-775c-46a6-9767-ce76838433d4", "story_id":"storyID2",
             * "snow_response":{ "result":{
             * "message":"Successfully created incident with id 4bf8880e2f6d81108bfb56e62799b6c8", "incident":{
             * "sysId":"4bf8880e2f6d81108bfb56e62799b6c8", "incidentNumber":"INC0021356" }, "status":"success" } },
             * "permalink":
             * "https://dev109758.service-now.com/now/workspace/agent/record/incident/4bf8880e2f6d81108bfb56e62799b6c8"
             * }
             */
            // Try to create source URL, if none can be set use the default one
            URI sourceURI = null;
            try {
                sourceURI = new URI(source);
            } catch (Exception e) {
                sourceURI = Connector.SELF_SOURCE;
            }
            ce = connector.createEvent(0, ConnectorConstants.INCIDENT_CREATE_R, json.toString(), sourceURI);
            connector.emitCloudEvent(ConnectorConstants.TOPIC_OUTPUT_ITSM_INCIDENT_RESPONSE, storyId, ce);

        } catch (InterruptedException e) {
            actionErrorCounter.increment();
            logger.log(Level.SEVERE, "Incident creation failed ", e);
        } catch (Exception e) {
            actionErrorCounter.increment();
            logger.log(Level.SEVERE, e.getMessage(), e);
            connector.triggerAlerts(config, e, bodyString);
        }
        logger.log(Level.INFO, "Incident creation successful");
        connector.clearAlerts(config, ConnectorConstants.ALERT_TYPES_LIST, connector.getTimeLastCleared());

    }
}
